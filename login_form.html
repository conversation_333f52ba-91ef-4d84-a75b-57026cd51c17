﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            position: relative;
            overflow: hidden;
        }
        .login-container::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-header h1 {
            color: #333;
            font-size: 2em;
            margin-bottom: 10px;
        }
        .login-header p {
            color: #666;
            font-size: 0.9em;
        }
        .form-group {
            margin-bottom: 20px;
            position: relative;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .form-group .icon {
            position: absolute;
            right: 15px;
            top: 38px;
            color: #999;
            font-size: 1.2em;
        }
        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            font-size: 0.9em;
        }
        .remember-me {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .remember-me input[type="checkbox"] {
            width: auto;
        }
        .forgot-password {
            color: #667eea;
            text-decoration: none;
            transition: color 0.3s;
        }
        .forgot-password:hover {
            color: #764ba2;
        }
        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .login-btn:active {
            transform: translateY(0);
        }
        .divider {
            text-align: center;
            margin: 25px 0;
            position: relative;
            color: #999;
        }
        .divider::before {
            content: "";
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e1e8ed;
        }
        .divider span {
            background: white;
            padding: 0 15px;
        }
        .social-login {
            display: flex;
            gap: 10px;
            margin-bottom: 25px;
        }
        .social-btn {
            flex: 1;
            padding: 10px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 1.2em;
        }
        .social-btn:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }
        .signup-link {
            text-align: center;
            color: #666;
            font-size: 0.9em;
        }
        .signup-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }
        .signup-link a:hover {
            color: #764ba2;
        }
        .error-message {
            background: #ffe6e6;
            color: #d63031;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            border-left: 4px solid #d63031;
            display: none;
        }
        .success-message {
            background: #e6ffe6;
            color: #00b894;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            border-left: 4px solid #00b894;
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>欢迎回来</h1>
            <p>请登录您的账户</p>
        </div>

        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名或邮箱</label>
                <input type="text" id="username" name="username" required>
                <span class="icon">👤</span>
            </div>

            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required>
                <span class="icon">🔒</span>
            </div>

            <div class="remember-forgot">
                <div class="remember-me">
                    <input type="checkbox" id="remember" name="remember">
                    <label for="remember">记住我</label>
                </div>
                <a href="#" class="forgot-password">忘记密码？</a>
            </div>

            <button type="submit" class="login-btn">登录</button>
        </form>

        <div class="divider">
            <span>或者</span>
        </div>

        <div class="social-login">
            <button class="social-btn" onclick="socialLogin(\'wechat\')">💬</button>
            <button class="social-btn" onclick="socialLogin(\'qq\')">🐧</button>
            <button class="social-btn" onclick="socialLogin(\'weibo\')">📱</button>
        </div>

        <div class="signup-link">
            还没有账户？ <a href="#">立即注册</a>
        </div>
    </div>

    <script>
        // 表单提交处理
        document.getElementById("loginForm").addEventListener("submit", function(e) {
            e.preventDefault();

            const username = document.getElementById("username").value;
            const password = document.getElementById("password").value;
            const errorMsg = document.getElementById("errorMessage");
            const successMsg = document.getElementById("successMessage");

            // 隐藏之前的消息
            errorMsg.style.display = "none";
            successMsg.style.display = "none";

            // 简单验证
            if (username.length < 3) {
                showError("用户名至少需要3个字符");
                return;
            }

            if (password.length < 6) {
                showError("密码至少需要6个字符");
                return;
            }

            // 模拟登录过程
            const loginBtn = document.querySelector(".login-btn");
            loginBtn.textContent = "登录中...";
            loginBtn.disabled = true;

            setTimeout(() => {
                // 模拟登录成功
                if (username === "admin" && password === "123456") {
                    showSuccess("登录成功！欢迎回来，" + username);
                    setTimeout(() => {
                        alert("登录成功！这是一个演示页面。");
                    }, 1000);
                } else {
                    showError("用户名或密码错误，请重试");
                }

                loginBtn.textContent = "登录";
                loginBtn.disabled = false;
            }, 1500);
        });

        // 显示错误消息
        function showError(message) {
            const errorMsg = document.getElementById("errorMessage");
            errorMsg.textContent = message;
            errorMsg.style.display = "block";

            // 3秒后自动隐藏
            setTimeout(() => {
                errorMsg.style.display = "none";
            }, 3000);
        }

        // 显示成功消息
        function showSuccess(message) {
            const successMsg = document.getElementById("successMessage");
            successMsg.textContent = message;
            successMsg.style.display = "block";
        }

        // 社交登录
        function socialLogin(platform) {
            const platforms = {
                wechat: "微信",
                qq: "QQ",
                weibo: "微博"
            };

            alert("正在跳转到" + platforms[platform] + "登录...\n这是一个演示功能。");
        }

        // 输入框焦点效果
        document.querySelectorAll("input").forEach(input => {
            input.addEventListener("focus", function() {
                this.parentElement.style.transform = "scale(1.02)";
                this.parentElement.style.transition = "transform 0.3s ease";
            });

            input.addEventListener("blur", function() {
                this.parentElement.style.transform = "scale(1)";
            });
        });

        // 页面加载动画
        window.addEventListener("load", function() {
            const container = document.querySelector(".login-container");
            container.style.opacity = "0";
            container.style.transform = "translateY(50px)";
            container.style.transition = "all 0.8s ease";

            setTimeout(() => {
                container.style.opacity = "1";
                container.style.transform = "translateY(0)";
            }, 100);
        });

        // 演示提示
        setTimeout(() => {
            alert("演示提示：\n用户名：admin\n密码：123456\n\n或者直接输入任意内容体验表单验证功能。");
        }, 2000);
    </script>
</body>
</html>