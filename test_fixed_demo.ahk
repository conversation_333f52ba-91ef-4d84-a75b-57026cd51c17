; 测试修复后的demo.ahk F3功能
#Requires AutoHotkey v2.0
#SingleInstance Force

; 包含demo.ahk中的函数
#Include demo.ahk

; 测试生成修复版交互网页
MsgBox("正在测试修复后的F3功能...", "测试开始", "OK")

; 调用函数生成网页
try {
    result := 生成修复版交互网页()
} catch Error as e {
    result := false
    MsgBox("调用函数时发生错误：" . e.Message, "错误", "OK Icon!")
}

if (result) {
    MsgBox("✅ 测试成功！`n`n修复内容：`n• 🔧 修复了JavaScript函数调用中的引号问题`n• ✅ 所有onclick事件现在使用正确的引号`n• 🎯 键盘事件处理已修复`n• 🧪 测试函数中的语法错误已解决`n`n现在所有交互功能都应该正常工作了！", "修复成功", "OK")
} else {
    MsgBox("❌ 测试失败！`n`n请检查脚本是否有其他错误。", "测试失败", "OK Icon!")
}

ExitApp()
