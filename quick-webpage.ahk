; AutoHotkey v2 脚本 - 快速网页生成器
; 描述：使用热键快速生成不同类型的网页
; 按F1-F4生成不同的网页模板

#Requires AutoHotkey v2.0
#SingleInstance Force

; ===========================================
; 热键定义 - 快速生成网页
; ===========================================

; F1键 - 生成简单个人介绍页面
F1:: {
    生成个人介绍页面()
    ToolTip("个人介绍页面已生成并打开", , , 1)
    SetTimer(() => ToolTip("", , , 1), -2000)
}

; F2键 - 生成产品展示页面
F2:: {
    生成产品展示页面()
    ToolTip("产品展示页面已生成并打开", , , 1)
    SetTimer(() => ToolTip("", , , 1), -2000)
}

; F3键 - 生成登录表单页面
F3:: {
    生成登录表单页面()
    ToolTip("登录表单页面已生成并打开", , , 1)
    SetTimer(() => ToolTip("", , , 1), -2000)
}

; F4键 - 生成图片画廊页面
F4:: {
    生成图片画廊页面()
    ToolTip("图片画廊页面已生成并打开", , , 1)
    SetTimer(() => ToolTip("", , , 1), -2000)
}

; F5键 - 显示帮助信息
F5:: {
    显示帮助信息()
}

; ESC键 - 退出脚本
Esc::ExitApp()

; ===========================================
; 网页生成函数
; ===========================================

; 生成个人介绍页面
生成个人介绍页面() {
    html内容 := '
    (
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人介绍 - 张三</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 50px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .header {
            text-align: center;
            padding: 30px 0;
            border-bottom: 2px solid #eee;
        }
        .avatar {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: #74b9ff;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 60px;
            color: white;
        }
        .name {
            font-size: 2.5em;
            color: #2d3436;
            margin-bottom: 10px;
        }
        .title {
            font-size: 1.2em;
            color: #636e72;
        }
        .section {
            padding: 30px 0;
            border-bottom: 1px solid #eee;
        }
        .section:last-child {
            border-bottom: none;
        }
        .section h2 {
            color: #2d3436;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        .skills {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .skill {
            background: #74b9ff;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
        }
        .contact {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 20px;
        }
        .contact-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            flex: 1;
            min-width: 200px;
        }
        .contact-item i {
            font-size: 2em;
            color: #74b9ff;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="avatar">张</div>
            <h1 class="name">张三</h1>
            <p class="title">全栈开发工程师</p>
        </div>
        
        <div class="section">
            <h2>关于我</h2>
            <p>我是一名热爱技术的全栈开发工程师，拥有5年的Web开发经验。专注于前端和后端技术，喜欢学习新技术并将其应用到实际项目中。</p>
        </div>
        
        <div class="section">
            <h2>技能专长</h2>
            <div class="skills">
                <span class="skill">JavaScript</span>
                <span class="skill">Python</span>
                <span class="skill">React</span>
                <span class="skill">Vue.js</span>
                <span class="skill">Node.js</span>
                <span class="skill">MySQL</span>
                <span class="skill">MongoDB</span>
                <span class="skill">Docker</span>
                <span class="skill">AWS</span>
                <span class="skill">Git</span>
            </div>
        </div>
        
        <div class="section">
            <h2>工作经历</h2>
            <div style="margin-bottom: 20px;">
                <h3>高级前端开发工程师 - ABC科技公司 (2021-至今)</h3>
                <p>负责公司主要产品的前端开发，使用React和Vue.js构建用户界面，优化用户体验。</p>
            </div>
            <div>
                <h3>全栈开发工程师 - XYZ创业公司 (2019-2021)</h3>
                <p>独立负责整个Web应用的开发，从前端到后端，从设计到部署。</p>
            </div>
        </div>
        
        <div class="section">
            <h2>联系方式</h2>
            <div class="contact">
                <div class="contact-item">
                    <div style="font-size: 2em; color: #74b9ff; margin-bottom: 10px;">📧</div>
                    <p><EMAIL></p>
                </div>
                <div class="contact-item">
                    <div style="font-size: 2em; color: #74b9ff; margin-bottom: 10px;">📱</div>
                    <p>138-0000-0000</p>
                </div>
                <div class="contact-item">
                    <div style="font-size: 2em; color: #74b9ff; margin-bottom: 10px;">🌐</div>
                    <p>github.com/zhangsan</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 简单的交互效果
        document.querySelectorAll(".skill").forEach(skill => {
            skill.addEventListener("mouseenter", function() {
                this.style.transform = "scale(1.1)";
                this.style.transition = "transform 0.3s";
            });
            
            skill.addEventListener("mouseleave", function() {
                this.style.transform = "scale(1)";
            });
        });
        
        // 页面加载动画
        window.addEventListener("load", function() {
            document.querySelector(".container").style.opacity = "0";
            document.querySelector(".container").style.transform = "translateY(50px)";
            document.querySelector(".container").style.transition = "all 0.8s ease";
            
            setTimeout(() => {
                document.querySelector(".container").style.opacity = "1";
                document.querySelector(".container").style.transform = "translateY(0)";
            }, 100);
        });
    </script>
</body>
</html>
    )'
    
    创建并打开网页(html内容, "personal_profile.html")
}

; 生成产品展示页面
生成产品展示页面() {
    html内容 := '
    (
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品展示 - 智能手表</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background: #f5f6fa;
            color: #2f3542;
        }
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
            text-align: center;
        }
        .hero h1 {
            font-size: 3.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .hero p {
            font-size: 1.3em;
            margin-bottom: 30px;
        }
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: #ff6b6b;
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: bold;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .features {
            padding: 80px 0;
            background: white;
        }
        .features h2 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 60px;
            color: #2f3542;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
        }
        .feature-card {
            text-align: center;
            padding: 40px 20px;
            border-radius: 15px;
            background: #f8f9fa;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .feature-icon {
            font-size: 4em;
            margin-bottom: 20px;
        }
        .feature-card h3 {
            font-size: 1.5em;
            margin-bottom: 15px;
            color: #2f3542;
        }
        .specs {
            padding: 80px 0;
            background: #f8f9fa;
        }
        .specs h2 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 60px;
            color: #2f3542;
        }
        .spec-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .spec-row {
            display: flex;
            border-bottom: 1px solid #eee;
        }
        .spec-row:last-child {
            border-bottom: none;
        }
        .spec-label {
            flex: 1;
            padding: 20px;
            background: #667eea;
            color: white;
            font-weight: bold;
        }
        .spec-value {
            flex: 2;
            padding: 20px;
        }
        .price {
            text-align: center;
            padding: 80px 0;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }
        .price h2 {
            font-size: 2.5em;
            margin-bottom: 20px;
        }
        .price-value {
            font-size: 4em;
            font-weight: bold;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <div class="hero">
        <div class="container">
            <h1>智能手表 Pro</h1>
            <p>科技与时尚的完美结合，让生活更智能</p>
            <a href="#features" class="btn">了解更多</a>
        </div>
    </div>
    
    <div class="features" id="features">
        <div class="container">
            <h2>产品特色</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">⌚</div>
                    <h3>精准计时</h3>
                    <p>瑞士机芯，精确到秒，永不掉队的时间伙伴</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">💓</div>
                    <h3>健康监测</h3>
                    <p>24小时心率监测，睡眠分析，让健康看得见</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🏃</div>
                    <h3>运动追踪</h3>
                    <p>多种运动模式，GPS定位，专业运动数据分析</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">💧</div>
                    <h3>防水设计</h3>
                    <p>50米防水，游泳、洗澡都不怕，全天候陪伴</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔋</div>
                    <h3>长续航</h3>
                    <p>7天超长续航，磁吸充电，告别频繁充电烦恼</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <h3>智能连接</h3>
                    <p>蓝牙5.0，消息提醒，通话功能，手腕上的智能助手</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="specs">
        <div class="container">
            <h2>技术规格</h2>
            <div class="spec-table">
                <div class="spec-row">
                    <div class="spec-label">显示屏</div>
                    <div class="spec-value">1.4英寸 AMOLED 高清彩屏</div>
                </div>
                <div class="spec-row">
                    <div class="spec-label">处理器</div>
                    <div class="spec-value">双核 ARM Cortex-M4</div>
                </div>
                <div class="spec-row">
                    <div class="spec-label">内存</div>
                    <div class="spec-value">512MB RAM + 4GB 存储</div>
                </div>
                <div class="spec-row">
                    <div class="spec-label">电池</div>
                    <div class="spec-value">420mAh 锂电池，7天续航</div>
                </div>
                <div class="spec-row">
                    <div class="spec-label">连接</div>
                    <div class="spec-value">蓝牙 5.0, Wi-Fi, GPS</div>
                </div>
                <div class="spec-row">
                    <div class="spec-label">传感器</div>
                    <div class="spec-value">心率、加速度、陀螺仪、气压</div>
                </div>
                <div class="spec-row">
                    <div class="spec-label">防水等级</div>
                    <div class="spec-value">5ATM (50米防水)</div>
                </div>
                <div class="spec-row">
                    <div class="spec-label">重量</div>
                    <div class="spec-value">45g (不含表带)</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="price">
        <div class="container">
            <h2>特惠价格</h2>
            <div class="price-value">¥1,299</div>
            <a href="#" class="btn" onclick="alert('感谢您的关注！请联系客服购买。')">立即购买</a>
        </div>
    </div>
    
    <script>
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 滚动动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // 观察所有特色卡片
        document.querySelectorAll('.feature-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(50px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    </script>
</body>
</html>
    )'
    
    创建并打开网页(html内容, "product_showcase.html")
}

; 通用函数：创建并打开网页
创建并打开网页(html内容, 文件名) {
    try {
        ; 创建文件路径
        文件路径 := A_ScriptDir . "\" . 文件名
        
        ; 删除可能存在的旧文件
        if FileExist(文件路径) {
            FileDelete(文件路径)
        }
        
        ; 写入HTML内容
        FileAppend(html内容, 文件路径, "UTF-8")
        
        ; 在默认浏览器中打开
        Run(文件路径)
        
        return true
        
    } catch Error as e {
        MsgBox("创建网页失败：" . e.Message, "错误", "OK Icon!")
        return false
    }
}

; 生成登录表单页面
生成登录表单页面() {
    html内容 := '
    (
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            position: relative;
            overflow: hidden;
        }
        .login-container::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-header h1 {
            color: #333;
            font-size: 2em;
            margin-bottom: 10px;
        }
        .login-header p {
            color: #666;
            font-size: 0.9em;
        }
        .form-group {
            margin-bottom: 20px;
            position: relative;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .form-group .icon {
            position: absolute;
            right: 15px;
            top: 38px;
            color: #999;
            font-size: 1.2em;
        }
        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            font-size: 0.9em;
        }
        .remember-me {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .remember-me input[type="checkbox"] {
            width: auto;
        }
        .forgot-password {
            color: #667eea;
            text-decoration: none;
            transition: color 0.3s;
        }
        .forgot-password:hover {
            color: #764ba2;
        }
        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .login-btn:active {
            transform: translateY(0);
        }
        .divider {
            text-align: center;
            margin: 25px 0;
            position: relative;
            color: #999;
        }
        .divider::before {
            content: "";
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e1e8ed;
        }
        .divider span {
            background: white;
            padding: 0 15px;
        }
        .social-login {
            display: flex;
            gap: 10px;
            margin-bottom: 25px;
        }
        .social-btn {
            flex: 1;
            padding: 10px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 1.2em;
        }
        .social-btn:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }
        .signup-link {
            text-align: center;
            color: #666;
            font-size: 0.9em;
        }
        .signup-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }
        .signup-link a:hover {
            color: #764ba2;
        }
        .error-message {
            background: #ffe6e6;
            color: #d63031;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            border-left: 4px solid #d63031;
            display: none;
        }
        .success-message {
            background: #e6ffe6;
            color: #00b894;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            border-left: 4px solid #00b894;
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>欢迎回来</h1>
            <p>请登录您的账户</p>
        </div>

        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名或邮箱</label>
                <input type="text" id="username" name="username" required>
                <span class="icon">👤</span>
            </div>

            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required>
                <span class="icon">🔒</span>
            </div>

            <div class="remember-forgot">
                <div class="remember-me">
                    <input type="checkbox" id="remember" name="remember">
                    <label for="remember">记住我</label>
                </div>
                <a href="#" class="forgot-password">忘记密码？</a>
            </div>

            <button type="submit" class="login-btn">登录</button>
        </form>

        <div class="divider">
            <span>或者</span>
        </div>

        <div class="social-login">
            <button class="social-btn" onclick="socialLogin('wechat')">💬</button>
            <button class="social-btn" onclick="socialLogin('qq')">🐧</button>
            <button class="social-btn" onclick="socialLogin('weibo')">📱</button>
        </div>

        <div class="signup-link">
            还没有账户？ <a href="#">立即注册</a>
        </div>
    </div>

    <script>
        // 表单提交处理
        document.getElementById("loginForm").addEventListener("submit", function(e) {
            e.preventDefault();

            const username = document.getElementById("username").value;
            const password = document.getElementById("password").value;
            const errorMsg = document.getElementById("errorMessage");
            const successMsg = document.getElementById("successMessage");

            // 隐藏之前的消息
            errorMsg.style.display = "none";
            successMsg.style.display = "none";

            // 简单验证
            if (username.length < 3) {
                showError("用户名至少需要3个字符");
                return;
            }

            if (password.length < 6) {
                showError("密码至少需要6个字符");
                return;
            }

            // 模拟登录过程
            const loginBtn = document.querySelector(".login-btn");
            loginBtn.textContent = "登录中...";
            loginBtn.disabled = true;

            setTimeout(() => {
                // 模拟登录成功
                if (username === "admin" && password === "123456") {
                    showSuccess("登录成功！欢迎回来，" + username);
                    setTimeout(() => {
                        alert("登录成功！这是一个演示页面。");
                    }, 1000);
                } else {
                    showError("用户名或密码错误，请重试");
                }

                loginBtn.textContent = "登录";
                loginBtn.disabled = false;
            }, 1500);
        });

        // 显示错误消息
        function showError(message) {
            const errorMsg = document.getElementById("errorMessage");
            errorMsg.textContent = message;
            errorMsg.style.display = "block";

            // 3秒后自动隐藏
            setTimeout(() => {
                errorMsg.style.display = "none";
            }, 3000);
        }

        // 显示成功消息
        function showSuccess(message) {
            const successMsg = document.getElementById("successMessage");
            successMsg.textContent = message;
            successMsg.style.display = "block";
        }

        // 社交登录
        function socialLogin(platform) {
            const platforms = {
                wechat: "微信",
                qq: "QQ",
                weibo: "微博"
            };

            alert("正在跳转到" + platforms[platform] + "登录...\n这是一个演示功能。");
        }

        // 输入框焦点效果
        document.querySelectorAll("input").forEach(input => {
            input.addEventListener("focus", function() {
                this.parentElement.style.transform = "scale(1.02)";
                this.parentElement.style.transition = "transform 0.3s ease";
            });

            input.addEventListener("blur", function() {
                this.parentElement.style.transform = "scale(1)";
            });
        });

        // 页面加载动画
        window.addEventListener("load", function() {
            const container = document.querySelector(".login-container");
            container.style.opacity = "0";
            container.style.transform = "translateY(50px)";
            container.style.transition = "all 0.8s ease";

            setTimeout(() => {
                container.style.opacity = "1";
                container.style.transform = "translateY(0)";
            }, 100);
        });

        // 演示提示
        setTimeout(() => {
            alert("演示提示：\n用户名：admin\n密码：123456\n\n或者直接输入任意内容体验表单验证功能。");
        }, 2000);
    </script>
</body>
</html>
    )'

    创建并打开网页(html内容, "login_form.html")
}

; 生成图片画廊页面
生成图片画廊页面() {
    html内容 := '
    (
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片画廊</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            overflow-x: hidden;
        }
        .header {
            text-align: center;
            padding: 60px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .header h1 {
            font-size: 3em;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        .filter-tabs {
            display: flex;
            justify-content: center;
            gap: 20px;
            padding: 40px 20px;
            flex-wrap: wrap;
        }
        .filter-btn {
            padding: 10px 25px;
            background: transparent;
            color: white;
            border: 2px solid #667eea;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
        }
        .filter-btn:hover,
        .filter-btn.active {
            background: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 0 20px 60px;
            max-width: 1400px;
            margin: 0 auto;
        }
        .gallery-item {
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #333;
            aspect-ratio: 4/3;
        }
        .gallery-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.5);
        }
        .gallery-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
            border-radius: 15px;
        }
        .gallery-item:hover img {
            transform: scale(1.1);
        }
        .gallery-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            padding: 30px 20px 20px;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }
        .gallery-item:hover .gallery-overlay {
            transform: translateY(0);
        }
        .gallery-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .gallery-description {
            font-size: 0.9em;
            opacity: 0.8;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        .modal-content {
            position: relative;
            max-width: 90%;
            max-height: 90%;
            border-radius: 10px;
            overflow: hidden;
        }
        .modal-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        .modal-close {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.7);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .modal-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0,0,0,0.7);
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s;
        }
        .modal-nav:hover {
            background: rgba(0,0,0,0.9);
        }
        .modal-prev {
            left: 20px;
        }
        .modal-next {
            right: 20px;
        }
        .modal-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            padding: 40px 20px 20px;
            text-align: center;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 40px;
            font-size: 1.2em;
        }
        .spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #333;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>精美图片画廊</h1>
        <p>探索美丽的世界，发现生活中的精彩瞬间</p>
    </div>

    <div class="filter-tabs">
        <button class="filter-btn active" data-filter="all">全部</button>
        <button class="filter-btn" data-filter="nature">自然风光</button>
        <button class="filter-btn" data-filter="city">城市建筑</button>
        <button class="filter-btn" data-filter="people">人物摄影</button>
        <button class="filter-btn" data-filter="art">艺术创作</button>
    </div>

    <div class="loading">
        <div class="spinner"></div>
        <p>正在加载图片...</p>
    </div>

    <div class="gallery" id="gallery">
        <!-- 图片将通过JavaScript动态生成 -->
    </div>

    <!-- 模态框 -->
    <div class="modal" id="modal">
        <div class="modal-content">
            <button class="modal-close" onclick="closeModal()">&times;</button>
            <button class="modal-nav modal-prev" onclick="prevImage()">&#8249;</button>
            <button class="modal-nav modal-next" onclick="nextImage()">&#8250;</button>
            <img class="modal-image" id="modalImage" src="" alt="">
            <div class="modal-info">
                <h3 id="modalTitle"></h3>
                <p id="modalDescription"></p>
            </div>
        </div>
    </div>

    <script>
        // 图片数据 - 使用 Canvas 生成的彩色占位图片
        const images = [
            {
                id: 1,
                title: "山间晨雾",
                description: "清晨的山谷中，薄雾缭绕，宛如仙境",
                category: "nature",
                color: "#4CAF50"
            },
            {
                id: 2,
                title: "现代都市",
                description: "繁华都市的夜景，灯火辉煌",
                category: "city",
                color: "#2196F3"
            },
            {
                id: 3,
                title: "人像写真",
                description: "自然光下的人物肖像，展现真实美感",
                category: "people",
                color: "#FF9800"
            },
            {
                id: 4,
                title: "抽象艺术",
                description: "色彩与形状的完美结合",
                category: "art",
                color: "#9C27B0"
            },
            {
                id: 5,
                title: "海边日落",
                description: "夕阳西下，海天一色的壮美景象",
                category: "nature",
                color: "#FF5722"
            },
            {
                id: 6,
                title: "建筑几何",
                description: "现代建筑的几何美学",
                category: "city",
                color: "#607D8B"
            },
            {
                id: 7,
                title: "街头摄影",
                description: "捕捉城市生活中的精彩瞬间",
                category: "people",
                color: "#795548"
            },
            {
                id: 8,
                title: "创意设计",
                description: "独特的视觉创意表达",
                category: "art",
                color: "#E91E63"
            },
            {
                id: 9,
                title: "森林深处",
                description: "神秘的森林小径，阳光透过树叶",
                category: "nature",
                color: "#8BC34A"
            },
            {
                id: 10,
                title: "城市夜景",
                description: "霓虹灯下的繁华都市",
                category: "city",
                color: "#3F51B5"
            },
            {
                id: 11,
                title: "肖像艺术",
                description: "光影交错的人物特写",
                category: "people",
                color: "#FFC107"
            },
            {
                id: 12,
                title: "现代艺术",
                description: "几何与色彩的艺术碰撞",
                category: "art",
                color: "#00BCD4"
            }
        ];

        // 生成彩色占位图片
        function generatePlaceholderImage(width, height, color, text) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = width;
            canvas.height = height;

            // 创建径向渐变背景
            const gradient = ctx.createRadialGradient(width/2, height/2, 0, width/2, height/2, Math.max(width, height)/2);
            gradient.addColorStop(0, adjustBrightness(color, 30));
            gradient.addColorStop(0.7, color);
            gradient.addColorStop(1, adjustBrightness(color, -30));

            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);

            // 添加几何装饰图案
            ctx.fillStyle = 'rgba(255, 255, 255, 0.15)';

            // 绘制三角形装饰
            for (let i = 0; i < 8; i++) {
                const x = Math.random() * width;
                const y = Math.random() * height;
                const size = Math.random() * 40 + 20;

                ctx.beginPath();
                ctx.moveTo(x, y - size/2);
                ctx.lineTo(x - size/2, y + size/2);
                ctx.lineTo(x + size/2, y + size/2);
                ctx.closePath();
                ctx.fill();
            }

            // 绘制圆形装饰
            ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
            for (let i = 0; i < 12; i++) {
                const x = Math.random() * width;
                const y = Math.random() * height;
                const radius = Math.random() * 25 + 15;
                ctx.beginPath();
                ctx.arc(x, y, radius, 0, Math.PI * 2);
                ctx.fill();
            }

            // 添加主标题
            ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
            ctx.font = 'bold 28px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
            ctx.shadowBlur = 4;
            ctx.shadowOffsetX = 2;
            ctx.shadowOffsetY = 2;
            ctx.fillText(text, width / 2, height / 2);

            // 添加副标题
            ctx.font = '16px Microsoft YaHei';
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.shadowBlur = 2;
            ctx.fillText('演示图片', width / 2, height / 2 + 40);

            // 重置阴影
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;

            return canvas.toDataURL();
        }

        // 调整颜色亮度
        function adjustBrightness(color, amount) {
            const usePound = color[0] === '#';
            const col = usePound ? color.slice(1) : color;
            const num = parseInt(col, 16);
            let r = (num >> 16) + amount;
            let g = (num >> 8 & 0x00FF) + amount;
            let b = (num & 0x0000FF) + amount;
            r = r > 255 ? 255 : r < 0 ? 0 : r;
            g = g > 255 ? 255 : g < 0 ? 0 : g;
            b = b > 255 ? 255 : b < 0 ? 0 : b;
            return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
        }

        // 为每个图片生成占位图
        images.forEach(image => {
            image.src = generatePlaceholderImage(400, 300, image.color, image.title);
        });

        let currentImageIndex = 0;
        let filteredImages = [...images];

        // 初始化画廊
        function initGallery() {
            showLoading();

            // 显示生成提示
            document.querySelector('.loading p').textContent = '正在生成精美图片...';

            setTimeout(() => {
                try {
                    renderGallery(images);
                    hideLoading();

                    // 显示成功提示
                    setTimeout(() => {
                        alert('🎉 图片画廊加载完成！\n\n✨ 特色功能：\n• 点击图片查看大图\n• 使用分类筛选图片\n• 支持键盘导航（←→键切换图片）\n• 点击背景或按ESC关闭大图\n\n所有图片均为动态生成的彩色占位图，展示了完整的画廊功能！');
                    }, 500);
                } catch (error) {
                    console.error('画廊初始化失败:', error);
                    document.querySelector('.loading p').textContent = '加载失败，请刷新页面重试';
                }
            }, 1500);
        }

        // 渲染画廊
        function renderGallery(imageList) {
            const gallery = document.getElementById("gallery");
            gallery.innerHTML = "";

            imageList.forEach((image, index) => {
                const item = document.createElement("div");
                item.className = "gallery-item";
                item.onclick = () => openModal(index);

                item.innerHTML = `
                    <img src="${image.src}" alt="${image.title}" loading="lazy">
                    <div class="gallery-overlay">
                        <div class="gallery-title">${image.title}</div>
                        <div class="gallery-description">${image.description}</div>
                    </div>
                `;

                gallery.appendChild(item);
            });

            filteredImages = imageList;
        }

        // 过滤功能
        document.querySelectorAll(".filter-btn").forEach(btn => {
            btn.addEventListener("click", function() {
                // 更新按钮状态
                document.querySelectorAll(".filter-btn").forEach(b => b.classList.remove("active"));
                this.classList.add("active");

                // 过滤图片
                const filter = this.dataset.filter;
                const filtered = filter === "all" ? images : images.filter(img => img.category === filter);

                showLoading();
                setTimeout(() => {
                    renderGallery(filtered);
                    hideLoading();
                }, 500);
            });
        });

        // 打开模态框
        function openModal(index) {
            currentImageIndex = index;
            const image = filteredImages[index];

            document.getElementById("modalImage").src = image.src;
            document.getElementById("modalTitle").textContent = image.title;
            document.getElementById("modalDescription").textContent = image.description;
            document.getElementById("modal").style.display = "flex";

            // 禁止页面滚动
            document.body.style.overflow = "hidden";
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById("modal").style.display = "none";
            document.body.style.overflow = "auto";
        }

        // 上一张图片
        function prevImage() {
            currentImageIndex = (currentImageIndex - 1 + filteredImages.length) % filteredImages.length;
            const image = filteredImages[currentImageIndex];

            document.getElementById("modalImage").src = image.src;
            document.getElementById("modalTitle").textContent = image.title;
            document.getElementById("modalDescription").textContent = image.description;
        }

        // 下一张图片
        function nextImage() {
            currentImageIndex = (currentImageIndex + 1) % filteredImages.length;
            const image = filteredImages[currentImageIndex];

            document.getElementById("modalImage").src = image.src;
            document.getElementById("modalTitle").textContent = image.title;
            document.getElementById("modalDescription").textContent = image.description;
        }

        // 显示加载动画
        function showLoading() {
            document.querySelector(".loading").style.display = "block";
            document.getElementById("gallery").style.display = "none";
        }

        // 隐藏加载动画
        function hideLoading() {
            document.querySelector(".loading").style.display = "none";
            document.getElementById("gallery").style.display = "grid";
        }

        // 键盘事件
        document.addEventListener("keydown", function(e) {
            if (document.getElementById("modal").style.display === "flex") {
                switch(e.key) {
                    case "Escape":
                        closeModal();
                        break;
                    case "ArrowLeft":
                        prevImage();
                        break;
                    case "ArrowRight":
                        nextImage();
                        break;
                }
            }
        });

        // 点击模态框背景关闭
        document.getElementById("modal").addEventListener("click", function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 初始化
        initGallery();

        // 图片懒加载优化
        if ("IntersectionObserver" in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src || img.src;
                        img.classList.remove("lazy");
                        observer.unobserve(img);
                    }
                });
            });

            // 观察所有图片
            setTimeout(() => {
                document.querySelectorAll("img").forEach(img => {
                    imageObserver.observe(img);
                });
            }, 1100);
        }
    </script>
</body>
</html>
    )'

    创建并打开网页(html内容, "image_gallery.html")
}

; 显示帮助信息
显示帮助信息() {
    帮助内容 := "
    (
    AutoHotkey 快速网页生成器

    使用说明：
    F1 - 生成个人介绍页面
    F2 - 生成产品展示页面
    F3 - 生成登录表单页面
    F4 - 生成图片画廊页面
    F5 - 显示此帮助信息
    ESC - 退出程序

    功能特点：
    • 一键生成完整的HTML页面
    • 包含现代化的CSS样式
    • 响应式设计，适配各种设备
    • 包含JavaScript交互效果
    • 自动在浏览器中打开预览

    生成的文件保存在脚本同目录下
    可以直接用于网站部署或进一步修改

    页面详情：
    F1 - 个人介绍：包含头像、技能、经历等
    F2 - 产品展示：现代化产品展示页面
    F3 - 登录表单：带验证的用户登录界面
    F4 - 图片画廊：可过滤的响应式图片展示

    版本：1.0
    )"

    MsgBox(帮助内容, "使用帮助", "OK")
}
