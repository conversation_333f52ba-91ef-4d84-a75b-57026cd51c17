<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片画廊</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            overflow-x: hidden;
        }
        .header {
            text-align: center;
            padding: 60px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .header h1 {
            font-size: 3em;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        .filter-tabs {
            display: flex;
            justify-content: center;
            gap: 20px;
            padding: 40px 20px;
            flex-wrap: wrap;
        }
        .filter-btn {
            padding: 10px 25px;
            background: transparent;
            color: white;
            border: 2px solid #667eea;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
        }
        .filter-btn:hover,
        .filter-btn.active {
            background: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 0 20px 60px;
            max-width: 1400px;
            margin: 0 auto;
        }
        .gallery-item {
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #333;
            aspect-ratio: 4/3;
        }
        .gallery-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.5);
        }
        .gallery-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
            border-radius: 15px;
        }
        .gallery-item:hover img {
            transform: scale(1.1);
        }
        .gallery-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            padding: 30px 20px 20px;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }
        .gallery-item:hover .gallery-overlay {
            transform: translateY(0);
        }
        .gallery-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .gallery-description {
            font-size: 0.9em;
            opacity: 0.8;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        .modal-content {
            position: relative;
            max-width: 90%;
            max-height: 90%;
            border-radius: 10px;
            overflow: hidden;
        }
        .modal-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        .modal-close {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.7);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .modal-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0,0,0,0.7);
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s;
        }
        .modal-nav:hover {
            background: rgba(0,0,0,0.9);
        }
        .modal-prev {
            left: 20px;
        }
        .modal-next {
            right: 20px;
        }
        .modal-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            padding: 40px 20px 20px;
            text-align: center;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 40px;
            font-size: 1.2em;
        }
        .spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #333;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>精美图片画廊</h1>
        <p>探索美丽的世界，发现生活中的精彩瞬间</p>
    </div>

    <div class="filter-tabs">
        <button class="filter-btn active" data-filter="all">全部</button>
        <button class="filter-btn" data-filter="nature">自然风光</button>
        <button class="filter-btn" data-filter="city">城市建筑</button>
        <button class="filter-btn" data-filter="people">人物摄影</button>
        <button class="filter-btn" data-filter="art">艺术创作</button>
    </div>

    <div class="loading">
        <div class="spinner"></div>
        <p>正在加载图片...</p>
    </div>

    <div class="gallery" id="gallery">
        <!-- 图片将通过JavaScript动态生成 -->
    </div>

    <!-- 模态框 -->
    <div class="modal" id="modal">
        <div class="modal-content">
            <button class="modal-close" onclick="closeModal()">&times;</button>
            <button class="modal-nav modal-prev" onclick="prevImage()">&#8249;</button>
            <button class="modal-nav modal-next" onclick="nextImage()">&#8250;</button>
            <img class="modal-image" id="modalImage" src="" alt="">
            <div class="modal-info">
                <h3 id="modalTitle"></h3>
                <p id="modalDescription"></p>
            </div>
        </div>
    </div>

    <script>
        // 图片数据 - 使用 Canvas 生成的彩色占位图片
        const images = [
            {
                id: 1,
                title: "山间晨雾",
                description: "清晨的山谷中，薄雾缭绕，宛如仙境",
                category: "nature",
                color: "#4CAF50"
            },
            {
                id: 2,
                title: "现代都市",
                description: "繁华都市的夜景，灯火辉煌",
                category: "city",
                color: "#2196F3"
            },
            {
                id: 3,
                title: "人像写真",
                description: "自然光下的人物肖像，展现真实美感",
                category: "people",
                color: "#FF9800"
            },
            {
                id: 4,
                title: "抽象艺术",
                description: "色彩与形状的完美结合",
                category: "art",
                color: "#9C27B0"
            },
            {
                id: 5,
                title: "海边日落",
                description: "夕阳西下，海天一色的壮美景象",
                category: "nature",
                color: "#FF5722"
            },
            {
                id: 6,
                title: "建筑几何",
                description: "现代建筑的几何美学",
                category: "city",
                color: "#607D8B"
            },
            {
                id: 7,
                title: "街头摄影",
                description: "捕捉城市生活中的精彩瞬间",
                category: "people",
                color: "#795548"
            },
            {
                id: 8,
                title: "创意设计",
                description: "独特的视觉创意表达",
                category: "art",
                color: "#E91E63"
            },
            {
                id: 9,
                title: "森林深处",
                description: "神秘的森林小径，阳光透过树叶",
                category: "nature",
                color: "#8BC34A"
            },
            {
                id: 10,
                title: "城市夜景",
                description: "霓虹灯下的繁华都市",
                category: "city",
                color: "#3F51B5"
            },
            {
                id: 11,
                title: "肖像艺术",
                description: "光影交错的人物特写",
                category: "people",
                color: "#FFC107"
            },
            {
                id: 12,
                title: "现代艺术",
                description: "几何与色彩的艺术碰撞",
                category: "art",
                color: "#00BCD4"
            }
        ];

        // 生成彩色占位图片
        function generatePlaceholderImage(width, height, color, text) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = width;
            canvas.height = height;

            // 创建径向渐变背景
            const gradient = ctx.createRadialGradient(width/2, height/2, 0, width/2, height/2, Math.max(width, height)/2);
            gradient.addColorStop(0, adjustBrightness(color, 30));
            gradient.addColorStop(0.7, color);
            gradient.addColorStop(1, adjustBrightness(color, -30));

            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);

            // 添加几何装饰图案
            ctx.fillStyle = 'rgba(255, 255, 255, 0.15)';

            // 绘制三角形装饰
            for (let i = 0; i < 8; i++) {
                const x = Math.random() * width;
                const y = Math.random() * height;
                const size = Math.random() * 40 + 20;

                ctx.beginPath();
                ctx.moveTo(x, y - size/2);
                ctx.lineTo(x - size/2, y + size/2);
                ctx.lineTo(x + size/2, y + size/2);
                ctx.closePath();
                ctx.fill();
            }

            // 绘制圆形装饰
            ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
            for (let i = 0; i < 12; i++) {
                const x = Math.random() * width;
                const y = Math.random() * height;
                const radius = Math.random() * 25 + 15;
                ctx.beginPath();
                ctx.arc(x, y, radius, 0, Math.PI * 2);
                ctx.fill();
            }

            // 添加主标题
            ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
            ctx.font = 'bold 28px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
            ctx.shadowBlur = 4;
            ctx.shadowOffsetX = 2;
            ctx.shadowOffsetY = 2;
            ctx.fillText(text, width / 2, height / 2);

            // 添加副标题
            ctx.font = '16px Microsoft YaHei';
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.shadowBlur = 2;
            ctx.fillText('演示图片', width / 2, height / 2 + 40);

            // 重置阴影
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;

            return canvas.toDataURL();
        }

        // 调整颜色亮度
        function adjustBrightness(color, amount) {
            const usePound = color[0] === '#';
            const col = usePound ? color.slice(1) : color;
            const num = parseInt(col, 16);
            let r = (num >> 16) + amount;
            let g = (num >> 8 & 0x00FF) + amount;
            let b = (num & 0x0000FF) + amount;
            r = r > 255 ? 255 : r < 0 ? 0 : r;
            g = g > 255 ? 255 : g < 0 ? 0 : g;
            b = b > 255 ? 255 : b < 0 ? 0 : b;
            return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
        }

        // 为每个图片生成占位图
        images.forEach(image => {
            image.src = generatePlaceholderImage(400, 300, image.color, image.title);
        });

        let currentImageIndex = 0;
        let filteredImages = [...images];

        // 初始化画廊
        function initGallery() {
            showLoading();

            // 显示生成提示
            document.querySelector('.loading p').textContent = '正在生成精美图片...';

            setTimeout(() => {
                try {
                    renderGallery(images);
                    hideLoading();

                    // 显示成功提示
                    setTimeout(() => {
                        alert('🎉 图片画廊加载完成！\n\n✨ 特色功能：\n• 点击图片查看大图\n• 使用分类筛选图片\n• 支持键盘导航（←→键切换图片）\n• 点击背景或按ESC关闭大图\n\n所有图片均为动态生成的彩色占位图，展示了完整的画廊功能！');
                    }, 500);
                } catch (error) {
                    console.error('画廊初始化失败:', error);
                    document.querySelector('.loading p').textContent = '加载失败，请刷新页面重试';
                }
            }, 1500);
        }

        // 渲染画廊
        function renderGallery(imageList) {
            const gallery = document.getElementById("gallery");
            gallery.innerHTML = "";

            imageList.forEach((image, index) => {
                const item = document.createElement("div");
                item.className = "gallery-item";
                item.onclick = () => openModal(index);

                item.innerHTML = 
                    <img src="${image.src}" alt="${image.title}" loading="lazy">
                    <div class="gallery-overlay">
                        <div class="gallery-title">${image.title}</div>
                        <div class="gallery-description">${image.description}</div>
                    </div>
                ;

                gallery.appendChild(item);
            });

            filteredImages = imageList;
        }

        // 过滤功能
        document.querySelectorAll(".filter-btn").forEach(btn => {
            btn.addEventListener("click", function() {
                // 更新按钮状态
                document.querySelectorAll(".filter-btn").forEach(b => b.classList.remove("active"));
                this.classList.add("active");

                // 过滤图片
                const filter = this.dataset.filter;
                const filtered = filter === "all" ? images : images.filter(img => img.category === filter);

                showLoading();
                setTimeout(() => {
                    renderGallery(filtered);
                    hideLoading();
                }, 500);
            });
        });

        // 打开模态框
        function openModal(index) {
            currentImageIndex = index;
            const image = filteredImages[index];

            document.getElementById("modalImage").src = image.src;
            document.getElementById("modalTitle").textContent = image.title;
            document.getElementById("modalDescription").textContent = image.description;
            document.getElementById("modal").style.display = "flex";

            // 禁止页面滚动
            document.body.style.overflow = "hidden";
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById("modal").style.display = "none";
            document.body.style.overflow = "auto";
        }

        // 上一张图片
        function prevImage() {
            currentImageIndex = (currentImageIndex - 1 + filteredImages.length) % filteredImages.length;
            const image = filteredImages[currentImageIndex];

            document.getElementById("modalImage").src = image.src;
            document.getElementById("modalTitle").textContent = image.title;
            document.getElementById("modalDescription").textContent = image.description;
        }

        // 下一张图片
        function nextImage() {
            currentImageIndex = (currentImageIndex + 1) % filteredImages.length;
            const image = filteredImages[currentImageIndex];

            document.getElementById("modalImage").src = image.src;
            document.getElementById("modalTitle").textContent = image.title;
            document.getElementById("modalDescription").textContent = image.description;
        }

        // 显示加载动画
        function showLoading() {
            document.querySelector(".loading").style.display = "block";
            document.getElementById("gallery").style.display = "none";
        }

        // 隐藏加载动画
        function hideLoading() {
            document.querySelector(".loading").style.display = "none";
            document.getElementById("gallery").style.display = "grid";
        }

        // 键盘事件
        document.addEventListener("keydown", function(e) {
            if (document.getElementById("modal").style.display === "flex") {
                switch(e.key) {
                    case "Escape":
                        closeModal();
                        break;
                    case "ArrowLeft":
                        prevImage();
                        break;
                    case "ArrowRight":
                        nextImage();
                        break;
                }
            }
        });

        // 点击模态框背景关闭
        document.getElementById("modal").addEventListener("click", function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 初始化
        initGallery();

        // 图片懒加载优化
        if ("IntersectionObserver" in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src || img.src;
                        img.classList.remove("lazy");
                        observer.unobserve(img);
                    }
                });
            });

            // 观察所有图片
            setTimeout(() => {
                document.querySelectorAll("img").forEach(img => {
                    imageObserver.observe(img);
                });
            }, 1100);
        }
    </script>
</body>
</html>